<?php

namespace tests\unit\ai_sdr\mockery\agents;

use common\library\ai_agent\SdrEdmWriteAgent;
use common\library\ai_agent\AiAgentProcessResponse;
use tests\unit\ai_sdr\mockery\data\AgentResponseDataFactory;

/**
 * Mock SDR EDM写作Agent
 * 
 * 模拟SdrEdmWriteAgent的行为，用于测试
 */
class MockSdrEdmWriteAgent extends SdrEdmWriteAgent
{
    /**
     * @var AgentResponseDataFactory
     */
    private $dataFactory;
    
    /**
     * @var array 调用日志
     */
    private $callLog = [];

    public function __construct(int $clientId, int $userId, AgentResponseDataFactory $dataFactory)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->dataFactory = $dataFactory;
        
        // 不调用父类构造函数，避免真实的初始化
    }

    /**
     * Mock process方法
     */
    public function process(array $params = [], string $function = ''): array
    {
        // 记录调用日志
        $this->callLog[] = [
            'method' => 'process',
            'params' => $params,
            'function' => $function,
            'timestamp' => time(),
            'clientId' => $this->clientId,
            'userId' => $this->userId
        ];

        // 获取Mock响应数据
        $responseData = $this->dataFactory->getEdmWriteResponse($params);

        // 模拟formatAnswer处理
        $formattedData = $this->formatAnswer($responseData);

        // 返回与真实Agent相同的数组格式
        return [
            'answer' => $formattedData,
            'record_id' => $this->generateRecordId()
        ];
    }

    /**
     * Mock getAgentSceneType方法
     */
    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER;
    }

    /**
     * Mock formatAnswer方法
     */
    protected function formatAnswer(array $answer): array
    {
        // 模拟原始的格式化逻辑
        array_walk($answer, function (&$mail) {
            $content = $mail['content'] ?? '';
            $content = str_replace([
                "\\n", "\\t", "\\r"
            ], [
                "\n", "\t", "\r"
            ], $content);
            $mail['content'] = $content;
        });
        return $answer;
    }

    /**
     * 获取调用日志
     */
    public function getCallLog(): array
    {
        return $this->callLog;
    }

    /**
     * 清空调用日志
     */
    public function clearCallLog(): void
    {
        $this->callLog = [];
    }

    /**
     * 检查是否被调用过
     */
    public function wasCalled(): bool
    {
        return !empty($this->callLog);
    }

    /**
     * 获取调用次数
     */
    public function getCallCount(): int
    {
        return count($this->callLog);
    }

    /**
     * 获取最后一次调用的参数
     */
    public function getLastCallParams(): ?array
    {
        if (empty($this->callLog)) {
            return null;
        }
        
        return end($this->callLog)['params'];
    }

    /**
     * 验证调用参数
     */
    public function wasCalledWith(array $expectedParams): bool
    {
        foreach ($this->callLog as $call) {
            if ($this->paramsMatch($call['params'], $expectedParams)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 参数匹配检查
     */
    private function paramsMatch(array $actualParams, array $expectedParams): bool
    {
        foreach ($expectedParams as $key => $expectedValue) {
            if (!isset($actualParams[$key]) || $actualParams[$key] !== $expectedValue) {
                return false;
            }
        }
        return true;
    }

    /**
     * 设置自定义响应数据
     */
    public function setCustomResponse(array $responseData): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER,
            $responseData
        );
    }

    /**
     * Mock isSdr方法
     */
    public function isSdr(): bool
    {
        return true;
    }

    /**
     * Mock makeAgentProcessParams方法
     */
    public function makeAgentProcessParams(): array
    {
        return [
            'client_id' => $this->clientId,
            'user_id' => $this->userId,
            'scene_type' => $this->getAgentSceneType()
        ];
    }

    /**
     * 获取客户端ID
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * 生成记录ID
     */
    private function generateRecordId(): int
    {
        return mt_rand(100000, 999999);
    }

    /**
     * 模拟重试机制
     */
    public function processWithRetry(array $params = [], int $maxRetries = 3): array
    {
        $attempt = 0;
        $lastException = null;
        
        while ($attempt < $maxRetries) {
            try {
                $attempt++;
                $this->callLog[] = [
                    'method' => 'processWithRetry',
                    'attempt' => $attempt,
                    'params' => $params,
                    'timestamp' => time()
                ];
                
                $response = $this->process($params);
                return $response->answer;
                
            } catch (\Exception $e) {
                $lastException = $e;
                $this->callLog[] = [
                    'method' => 'processWithRetry',
                    'attempt' => $attempt,
                    'exception' => $e->getMessage(),
                    'timestamp' => time()
                ];
                
                if ($attempt >= $maxRetries) {
                    throw $lastException;
                }
                
                // 模拟重试延迟
                usleep(100000); // 0.1秒
            }
        }
        
        throw $lastException;
    }

    /**
     * 验证客户档案和买家档案参数
     */
    public function validateProfileParams(array $params): bool
    {
        $requiredKeys = ['client_profile', 'buyer_profile'];
        
        foreach ($requiredKeys as $key) {
            if (!isset($params[$key])) {
                return false;
            }
            
            // 验证是否为有效的JSON字符串
            $decoded = json_decode($params[$key], true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取邮件轮次数量
     */
    public function getEmailRoundsCount(array $params = []): int
    {
        $response = $this->process($params);
        return count($response->answer);
    }

    /**
     * 获取指定轮次的邮件内容
     */
    public function getEmailByRound(int $round, array $params = []): ?array
    {
        $response = $this->process($params);
        
        foreach ($response->answer as $email) {
            if (isset($email['round']) && $email['round'] === $round) {
                return $email;
            }
        }
        
        return null;
    }

    /**
     * 模拟异常情况
     */
    public function simulateException(string $message = 'Mock EDM Write Agent Exception'): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER,
            ['exception' => $message]
        );
    }

    /**
     * 重写process方法以支持异常模拟
     */
    public function processWithExceptionHandling(array $params = [], string $function = '')
    {
        try {
            $responseData = $this->dataFactory->getEdmWriteResponse($params);
            
            // 检查是否需要抛出异常
            if (isset($responseData['exception'])) {
                throw new \common\library\ai_agent\AiAgentException($responseData['exception']);
            }
            
            return $this->process($params, $function);
        } catch (\Exception $e) {
            $this->callLog[] = [
                'method' => 'process',
                'params' => $params,
                'function' => $function,
                'exception' => $e->getMessage(),
                'timestamp' => time()
            ];
            throw $e;
        }
    }
}
