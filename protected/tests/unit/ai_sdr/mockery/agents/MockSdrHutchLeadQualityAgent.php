<?php

namespace tests\unit\ai_sdr\mockery\agents;

use common\library\ai_agent\SdrHutchLeadQualityAgent;
use common\library\ai_agent\AiAgentProcessResponse;
use tests\unit\ai_sdr\mockery\data\AgentResponseDataFactory;

/**
 * Mock SDR Hutch线索质量分析Agent
 * 
 * 模拟SdrHutchLeadQualityAgent的行为，用于测试
 */
class MockSdrHutchLeadQualityAgent extends SdrHutchLeadQualityAgent
{
    /**
     * @var AgentResponseDataFactory
     */
    private $dataFactory;
    
    /**
     * @var array 调用日志
     */
    private $callLog = [];

    public function __construct(int $clientId, int $userId, AgentResponseDataFactory $dataFactory)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->dataFactory = $dataFactory;
        
        // 不调用父类构造函数，避免真实的初始化
    }

    /**
     * Mock process方法
     */
    public function process(array $params = [], string $function = ''):array
    {
        // 记录调用日志
        $this->callLog[] = [
            'method' => 'process',
            'params' => $params,
            'function' => $function,
            'timestamp' => time(),
            'clientId' => $this->clientId,
            'userId' => $this->userId
        ];

        // 获取Mock响应数据 - 使用质量分析的响应格式
        $responseData = $this->dataFactory->getQualityAnalysisResponse($params);
        
        // 模拟formatAnswer处理
        $formattedData = $this->formatAnswer($responseData['answer']);

        // 返回与真实Agent相同的数组格式
        return [
            'answer' => $formattedData,
            'record_id' => $responseData['record_id']
        ];
    }

    /**
     * Mock getAgentSceneType方法
     */
    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HUTCH_LEAD_QUALITY_ANALYSIS;
    }

    /**
     * Mock formatAnswer方法 (继承自SdrLeadQualityAnalysisAgent)
     */
    protected function formatAnswer(array $answer): array
    {
        // 模拟原始的格式化逻辑
        $leadQuality = $answer['lead_quality'] ?? '';
        if (str_contains($leadQuality, 'high')) {
            $answer['lead_quality'] = \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH;
        } elseif (str_contains($leadQuality, 'medium')) {
            $answer['lead_quality'] = \common\library\ai_sdr\Constant::LEAD_QUALITY_MEDIUM;
        } else {
            $answer['lead_quality'] = \common\library\ai_sdr\Constant::LEAD_QUALITY_LOW;
        }
        return $answer;
    }

    /**
     * Mock makeAgentProcessParams方法
     */
    public function makeAgentProcessParams(): array
    {
        return [
            'client_id' => $this->clientId,
            'user_id' => $this->userId,
            'scene_type' => $this->getAgentSceneType()
        ];
    }

    /**
     * 获取调用日志
     */
    public function getCallLog(): array
    {
        return $this->callLog;
    }

    /**
     * 清空调用日志
     */
    public function clearCallLog(): void
    {
        $this->callLog = [];
    }

    /**
     * 检查是否被调用过
     */
    public function wasCalled(): bool
    {
        return !empty($this->callLog);
    }

    /**
     * 获取调用次数
     */
    public function getCallCount(): int
    {
        return count($this->callLog);
    }

    /**
     * 获取最后一次调用的参数
     */
    public function getLastCallParams(): ?array
    {
        if (empty($this->callLog)) {
            return null;
        }
        
        return end($this->callLog)['params'];
    }

    /**
     * 验证调用参数
     */
    public function wasCalledWith(array $expectedParams): bool
    {
        foreach ($this->callLog as $call) {
            if ($this->paramsMatch($call['params'], $expectedParams)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 参数匹配检查
     */
    private function paramsMatch(array $actualParams, array $expectedParams): bool
    {
        foreach ($expectedParams as $key => $expectedValue) {
            if (!isset($actualParams[$key]) || $actualParams[$key] !== $expectedValue) {
                return false;
            }
        }
        return true;
    }

    /**
     * 设置自定义响应数据
     */
    public function setCustomResponse(array $responseData): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HUTCH_LEAD_QUALITY_ANALYSIS,
            $responseData
        );
    }

    /**
     * Mock isSdr方法
     */
    public function isSdr(): bool
    {
        return true;
    }

    /**
     * 获取客户端ID
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * 获取Hutch质量分析结果
     */
    public function getHutchQualityAnalysis(array $params = []): array
    {
        $response = $this->process($params);
        return $response->answer;
    }

    /**
     * 获取线索质量等级
     */
    public function getLeadQuality(array $params = []): string
    {
        $analysis = $this->getHutchQualityAnalysis($params);
        return $analysis['lead_quality'] ?? \common\library\ai_sdr\Constant::LEAD_QUALITY_UNKNOWN;
    }

    /**
     * 获取质量分析置信度
     */
    public function getQualityConfidence(array $params = []): float
    {
        $analysis = $this->getHutchQualityAnalysis($params);
        return $analysis['confidence'] ?? 0.0;
    }

    /**
     * 获取质量分析原因
     */
    public function getQualityReasons(array $params = []): array
    {
        $analysis = $this->getHutchQualityAnalysis($params);
        return $analysis['reason'] ?? [];
    }

    /**
     * 获取质量分数
     */
    public function getQualityScore(array $params = []): int
    {
        $analysis = $this->getHutchQualityAnalysis($params);
        return $analysis['score'] ?? 0;
    }

    /**
     * 检查是否为高质量线索
     */
    public function isHighQualityLead(array $params = []): bool
    {
        $quality = $this->getLeadQuality($params);
        return $quality === \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH;
    }

    /**
     * 检查是否为有效线索
     */
    public function isValidLead(array $params = []): bool
    {
        $quality = $this->getLeadQuality($params);
        return in_array($quality, [
            \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH,
            \common\library\ai_sdr\Constant::LEAD_QUALITY_MEDIUM
        ]);
    }

    /**
     * 设置Hutch质量分析场景
     */
    public function setHutchQualityScenario(string $scenario): void
    {
        switch ($scenario) {
            case 'high_quality_hutch':
                $this->setCustomResponse([
                    'answer' => [
                        'lead_quality' => \common\library\ai_sdr\Constant::LEAD_QUALITY_HIGH,
                        'confidence' => 0.92,
                        'reason' => ['Hutch数据完整', '匹配度高', '联系信息有效'],
                        'score' => 88,
                        'hutch_specific' => [
                            'data_completeness' => 'high',
                            'contact_validity' => 'verified',
                            'business_relevance' => 'strong'
                        ]
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'medium_quality_hutch':
                $this->setCustomResponse([
                    'answer' => [
                        'lead_quality' => \common\library\ai_sdr\Constant::LEAD_QUALITY_MEDIUM,
                        'confidence' => 0.75,
                        'reason' => ['Hutch数据部分完整', '有一定匹配度', '需要验证联系信息'],
                        'score' => 65,
                        'hutch_specific' => [
                            'data_completeness' => 'medium',
                            'contact_validity' => 'needs_verification',
                            'business_relevance' => 'moderate'
                        ]
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'low_quality_hutch':
                $this->setCustomResponse([
                    'answer' => [
                        'lead_quality' => \common\library\ai_sdr\Constant::LEAD_QUALITY_LOW,
                        'confidence' => 0.45,
                        'reason' => ['Hutch数据不完整', '匹配度低', '联系信息缺失'],
                        'score' => 35,
                        'hutch_specific' => [
                            'data_completeness' => 'low',
                            'contact_validity' => 'invalid',
                            'business_relevance' => 'weak'
                        ]
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
        }
    }

    /**
     * 模拟异常情况
     */
    public function simulateException(string $message = 'Mock Hutch Lead Quality Agent Exception'): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HUTCH_LEAD_QUALITY_ANALYSIS,
            ['exception' => $message]
        );
    }

    /**
     * 重写process方法以支持异常模拟
     */
    public function processWithExceptionHandling(array $params = [], string $function = '')
    {
        try {
            $responseData = $this->dataFactory->getQualityAnalysisResponse($params);
            
            // 检查是否需要抛出异常
            if (isset($responseData['exception'])) {
                throw new \common\library\ai_agent\AiAgentException($responseData['exception']);
            }
            
            return $this->process($params, $function);
        } catch (\Exception $e) {
            $this->callLog[] = [
                'method' => 'process',
                'params' => $params,
                'function' => $function,
                'exception' => $e->getMessage(),
                'timestamp' => time()
            ];
            throw $e;
        }
    }
}
