<?php

namespace tests\unit\ai_sdr\mockery\agents;

use common\library\ai_agent\AiAgentConstants;
use common\library\ai_agent\HomepageAiAgent;
use common\library\ai_agent\AiAgentProcessResponse;
use tests\unit\ai_sdr\mockery\data\AgentResponseDataFactory;

/**
 * Mock 主页分析AI Agent
 * 
 * 模拟HomepageAiAgent的行为，用于测试
 */
class MockHomepageAiAgent extends HomepageAiAgent
{
    /**
     * @var AgentResponseDataFactory
     */
    private $dataFactory;
    
    /**
     * @var array 调用日志
     */
    private $callLog = [];

    public function __construct(int $clientId, int $userId, AgentResponseDataFactory $dataFactory)
    {
        $this->clientId = $clientId;
        $this->userId = $userId;
        $this->dataFactory = $dataFactory;
        
        // 不调用父类构造函数，避免真实的初始化
    }

    /**
     * Mock process方法
     */
    public function process(array $params = [], string $function = ''): AiAgentProcessResponse
    {
        // 记录调用日志
        $this->callLog[] = [
            'method' => 'process',
            'params' => $params,
            'function' => $function,
            'timestamp' => time(),
            'clientId' => $this->clientId,
            'userId' => $this->userId
        ];

        // 获取Mock响应数据
        $responseData = $this->dataFactory->getHomepageAnalysisResponse($params);

        // 创建AiAgentProcessResponse对象
        $response = new AiAgentProcessResponse();
        $response->answer = json_encode($responseData['answer']); // 注意：真实Agent返回的answer是JSON字符串
        $response->recordId = $responseData['record_id'];
        $response->messageType = AiAgentConstants::AI_AGENT_MESSAGE_TYPE_TEXT;
        $response->function = $function ?: 'homepageAnalysis';

        return $response;
    }

    /**
     * Mock getAgentSceneType方法
     */
    public function getAgentSceneType(): int
    {
        return \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HOMEPAGE_SEARCH;
    }

    /**
     * Mock makeAgentProcessParams方法
     */
    public function makeAgentProcessParams(): array
    {
        return [
            'client_id' => $this->clientId,
            'user_id' => $this->userId,
            'scene_type' => $this->getAgentSceneType()
        ];
    }

    /**
     * 获取调用日志
     */
    public function getCallLog(): array
    {
        return $this->callLog;
    }

    /**
     * 清空调用日志
     */
    public function clearCallLog(): void
    {
        $this->callLog = [];
    }

    /**
     * 检查是否被调用过
     */
    public function wasCalled(): bool
    {
        return !empty($this->callLog);
    }

    /**
     * 获取调用次数
     */
    public function getCallCount(): int
    {
        return count($this->callLog);
    }

    /**
     * 获取最后一次调用的参数
     */
    public function getLastCallParams(): ?array
    {
        if (empty($this->callLog)) {
            return null;
        }
        
        return end($this->callLog)['params'];
    }

    /**
     * 验证调用参数
     */
    public function wasCalledWith(array $expectedParams): bool
    {
        foreach ($this->callLog as $call) {
            if ($this->paramsMatch($call['params'], $expectedParams)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 参数匹配检查
     */
    private function paramsMatch(array $actualParams, array $expectedParams): bool
    {
        foreach ($expectedParams as $key => $expectedValue) {
            if (!isset($actualParams[$key]) || $actualParams[$key] !== $expectedValue) {
                return false;
            }
        }
        return true;
    }

    /**
     * 设置自定义响应数据
     */
    public function setCustomResponse(array $responseData): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HOMEPAGE_SEARCH,
            $responseData
        );
    }

    /**
     * Mock isSdr方法
     */
    public function isSdr(): bool
    {
        return true;
    }

    /**
     * 获取客户端ID
     */
    public function getClientId(): int
    {
        return $this->clientId;
    }

    /**
     * 获取用户ID
     */
    public function getUserId(): int
    {
        return $this->userId;
    }

    /**
     * 获取主页分析结果
     */
    public function getHomepageAnalysis(array $params = []): array
    {
        $response = $this->process($params);
        return $response->answer;
    }

    /**
     * 获取公司信息
     */
    public function getCompanyInfo(array $params = []): array
    {
        $analysis = $this->getHomepageAnalysis($params);
        return $analysis['homepage_analysis']['company_info'] ?? [];
    }

    /**
     * 获取产品列表
     */
    public function getProducts(array $params = []): array
    {
        $analysis = $this->getHomepageAnalysis($params);
        return $analysis['homepage_analysis']['products'] ?? [];
    }

    /**
     * 获取服务列表
     */
    public function getServices(array $params = []): array
    {
        $analysis = $this->getHomepageAnalysis($params);
        return $analysis['homepage_analysis']['services'] ?? [];
    }

    /**
     * 获取联系信息
     */
    public function getContactInfo(array $params = []): array
    {
        $companyInfo = $this->getCompanyInfo($params);
        return $companyInfo['contact_info'] ?? [];
    }

    /**
     * 获取认证信息
     */
    public function getCertifications(array $params = []): array
    {
        $analysis = $this->getHomepageAnalysis($params);
        return $analysis['homepage_analysis']['certifications'] ?? [];
    }

    /**
     * 获取目标市场
     */
    public function getTargetMarkets(array $params = []): array
    {
        $analysis = $this->getHomepageAnalysis($params);
        return $analysis['homepage_analysis']['markets'] ?? [];
    }

    /**
     * 获取分析置信度
     */
    public function getConfidence(array $params = []): float
    {
        $analysis = $this->getHomepageAnalysis($params);
        return $analysis['confidence'] ?? 0.0;
    }

    /**
     * 验证公司名称参数
     */
    public function validateCompanyNameParam(array $params): bool
    {
        return isset($params['company_name']) && !empty($params['company_name']);
    }

    /**
     * 设置主页分析场景
     */
    public function setHomepageAnalysisScenario(string $scenario): void
    {
        switch ($scenario) {
            case 'tech_company':
                $this->setCustomResponse([
                    'answer' => [
                        'homepage_analysis' => [
                            'company_info' => [
                                'name' => 'TechCorp Solutions',
                                'description' => 'Leading technology solutions provider',
                                'contact_info' => [
                                    'email' => '<EMAIL>',
                                    'phone' => '******-TECH',
                                    'address' => '123 Innovation Drive, Tech Valley'
                                ]
                            ],
                            'products' => ['Software Solutions', 'Cloud Services', 'AI Tools'],
                            'services' => ['Consulting', 'Implementation', 'Support'],
                            'certifications' => ['ISO27001', 'SOC2', 'GDPR'],
                            'markets' => ['Global', 'Enterprise', 'B2B']
                        ],
                        'confidence' => 0.92
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'manufacturing_company':
                $this->setCustomResponse([
                    'answer' => [
                        'homepage_analysis' => [
                            'company_info' => [
                                'name' => 'Industrial Manufacturing Co.',
                                'description' => 'Quality manufacturing since 1995',
                                'contact_info' => [
                                    'email' => '<EMAIL>',
                                    'phone' => '+86-21-1234-5678',
                                    'address' => '456 Factory Road, Industrial Zone'
                                ]
                            ],
                            'products' => ['Industrial Equipment', 'Components', 'Machinery'],
                            'services' => ['Manufacturing', 'OEM', 'Quality Control'],
                            'certifications' => ['ISO9001', 'CE', 'UL'],
                            'markets' => ['Global', 'B2B', 'Industrial']
                        ],
                        'confidence' => 0.88
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
                
            case 'limited_info':
                $this->setCustomResponse([
                    'answer' => [
                        'homepage_analysis' => [
                            'company_info' => [
                                'name' => 'Unknown Company',
                                'description' => 'Limited information available',
                                'contact_info' => []
                            ],
                            'products' => [],
                            'services' => [],
                            'certifications' => [],
                            'markets' => []
                        ],
                        'confidence' => 0.25
                    ],
                    'record_id' => mt_rand(100000, 999999)
                ]);
                break;
        }
    }

    /**
     * 模拟异常情况
     */
    public function simulateException(string $message = 'Mock Homepage AI Agent Exception'): void
    {
        $this->dataFactory->setCustomResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HOMEPAGE_SEARCH,
            ['exception' => $message]
        );
    }

    /**
     * 重写process方法以支持异常模拟
     */
    public function processWithExceptionHandling(array $params = [], string $function = '')
    {
        try {
            $responseData = $this->dataFactory->getHomepageAnalysisResponse($params);
            
            // 检查是否需要抛出异常
            if (isset($responseData['exception'])) {
                throw new \common\library\ai_agent\AiAgentException($responseData['exception']);
            }
            
            return $this->process($params, $function);
        } catch (\Exception $e) {
            $this->callLog[] = [
                'method' => 'process',
                'params' => $params,
                'function' => $function,
                'exception' => $e->getMessage(),
                'timestamp' => time()
            ];
            throw $e;
        }
    }
}
