<?php

namespace tests\unit\ai_sdr\mockery;

use tests\unit\ai_sdr\AiSdrTestCase;
use tests\unit\ai_sdr\mockery\traits\MockeryAgentTrait;
use tests\unit\ai_sdr\mockery\AiAgentFactoryMockery;
use common\library\ai_sdr\Constant;
use common\library\ai_agent\AiAgentFactory;

/**
 * AI Agent Mockery框架集成测试
 *
 * 验证Mockery框架的完整功能和集成效果
 */
class AiAgentMockeryIntegrationTest extends AiSdrTestCase
{
    use MockeryAgentTrait;

    protected function setUp(): void
    {
        parent::setUp();
        $this->setUpAiAgentMockery();
    }

    protected function tearDown(): void
    {
        $this->tearDownAiAgentMockery();
        parent::tearDown();
    }

    /**
     * 测试所有Agent类型的Mock功能
     */
    public function testAllAgentTypesMocking()
    {
        $agentSceneTypes = [
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_PROFILE_GENERATION,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_BUYER_PROFILE_GENERATION,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HOMEPAGE_SEARCH,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_WASH_PRODUCT_CATEGORY,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HUTCH_LEAD_QUALITY_ANALYSIS
        ];

        foreach ($agentSceneTypes as $sceneType) {
            // 创建Agent
            $agent = AiAgentFactory::createAgent($sceneType, $this->getTestClientId(), $this->getTestUserId());

            // 验证Agent类型正确
            $this->assertNotNull($agent);
            $this->assertEquals($sceneType, $agent->getAgentSceneType());

            // 调用process方法
            $result = $agent->process(['test' => "scene_type_{$sceneType}"]);

            // 验证返回结果
            $this->assertNotNull($result);

            // 根据Agent类型验证返回格式
            if ($this->isArrayReturnAgent($sceneType)) {
                // 返回数组的Agent (继承自ProductUsageAgent)
                $this->assertIsArray($result);
                $this->assertArrayHasKey('answer', $result);
                $this->assertArrayHasKey('record_id', $result);
            } else {
                // 返回对象的Agent (继承自BaseAiAgent)
                $this->assertInstanceOf(\common\library\ai_agent\AiAgentProcessResponse::class, $result);
                $this->assertNotNull($result->answer);
                $this->assertNotNull($result->recordId);
            }

            // 验证调用被记录
            $this->assertAgentWasCalled($sceneType);
        }
        
        // 验证所有Agent都被调用了
        $this->assertAllExpectedAgentsCalled($agentSceneTypes);
        
        // 验证调用统计
        $statistics = $this->getAgentCallStatistics();
        $this->assertCount(count($agentSceneTypes), $statistics);
        
        foreach ($agentSceneTypes as $sceneType) {
            $this->assertEquals(1, $statistics[$sceneType]);
        }
    }

    /**
     * 测试质量分析Agent的详细功能
     */
    public function testQualityAnalysisAgentDetails()
    {
        // 清空之前的调用日志
        $this->clearAgentCallLog();

        // 测试高质量场景
        $this->setQualityAnalysisScenario('high_quality');
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->getTestClientId(),
            $this->getTestUserId()
        );
        
        $result = $agent->process([
            'client_profile' => json_encode(['company' => 'Test Corp']),
            'buyer_profile' => json_encode(['industry' => 'Technology'])
        ]);
        dd($result);
        // 验证高质量结果
        $this->assertEquals(Constant::LEAD_QUALITY_HIGH, $result['answer']['lead_quality']);
        $this->assertGreaterThan(0.8, $result['answer']['confidence']);
        $this->assertIsArray($result['answer']['reason']);
        $this->assertGreaterThan(80, $result['answer']['score']);
        
        // 测试中等质量场景
        $this->clearAgentCallLog();
        $this->setQualityAnalysisScenario('medium_quality');
        
        $result2 = $agent->process(['test' => 'medium']);
        
        $this->assertEquals(Constant::LEAD_QUALITY_MEDIUM, $result2['answer']['lead_quality']);
        $this->assertGreaterThan(0.6, $result2['answer']['confidence']);
        $this->assertLessThan(80, $result2['answer']['score']);
        
        // 验证调用次数
        $this->assertAgentCallCount(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS, 1);
    }

    /**
     * 测试EDM写作Agent的功能
     */
    public function testEdmWriteAgentDetails()
    {
        // 清空之前的调用日志
        $this->clearAgentCallLog();

        $this->setEdmWriteScenario('standard_emails');
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER,
            $this->getTestClientId(),
            $this->getTestUserId()
        );
        
        $result = $agent->process([
            'client_profile' => json_encode(['company' => 'Test Corp']),
            'buyer_profile' => json_encode(['industry' => 'Technology'])
        ]);
        
        // 验证邮件内容结构
        $this->assertIsArray($result['answer']);
        $this->assertCount(3, $result['answer']); // 3轮邮件

        foreach ($result['answer'] as $index => $email) {
            $this->assertArrayHasKey('subject', $email);
            $this->assertArrayHasKey('content', $email);
            $this->assertArrayHasKey('round', $email);
            $this->assertEquals($index + 1, $email['round']);
            $this->assertNotEmpty($email['subject']);
            $this->assertNotEmpty($email['content']);
        }

        // 验证内容格式化
        $firstEmail = $result['answer'][0];
        $this->assertStringContainsString('Partnership Opportunity', $firstEmail['subject']);
        $this->assertStringContainsString('Dear Partner', $firstEmail['content']);
    }

    /**
     * 测试卖家行业分析Agent
     */
    public function testSellerIndustryAnalysisAgent()
    {
        // 清空之前的调用日志
        $this->clearAgentCallLog();

        $this->setSellerIndustryAnalysisScenario('mixed_industries');
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE,
            $this->getTestClientId(),
            $this->getTestUserId()
        );
        
        $result = $agent->process(['test' => 'industry_analysis']);
        
        // 验证行业分析结果
        $this->assertIsArray($result['answer']);
        $this->assertGreaterThan(0, count($result['answer']));

        foreach ($result['answer'] as $industry) {
            $this->assertArrayHasKey('industry', $industry);
            $this->assertArrayHasKey('relative', $industry);
            $this->assertNotEmpty($industry['industry']);
            $this->assertContains($industry['relative'], [
                Constant::LEAD_QUALITY_HIGH,
                Constant::LEAD_QUALITY_MEDIUM,
                Constant::LEAD_QUALITY_LOW,
                Constant::LEAD_QUALITY_UNKNOWN
            ]);
        }
    }

    /**
     * 测试异常处理功能
     */
    public function testExceptionHandling()
    {
        // 清空之前的调用日志
        $this->clearAgentCallLog();

        // 模拟质量分析Agent异常
        $this->simulateAgentException(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            'Test exception message'
        );
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->getTestClientId(),
            $this->getTestUserId()
        );
        
        // 验证异常被正确抛出
        $this->expectException(\common\library\ai_agent\AiAgentException::class);
        $this->expectExceptionMessage('Test exception message');
        
        $agent->processWithExceptionHandling(['test' => 'exception']);
    }

    /**
     * 测试自定义响应数据
     */
    public function testCustomResponseData()
    {
        // 清空之前的调用日志
        $this->clearAgentCallLog();

        $customResponse = [
            'answer' => [
                'lead_quality' => Constant::LEAD_QUALITY_HIGH,
                'confidence' => 0.95,
                'reason' => ['Custom reason 1', 'Custom reason 2'],
                'score' => 92,
                'custom_field' => 'Custom test value',
                'timestamp' => time()
            ],
            'record_id' => 888999
        ];
        
        $this->setAgentResponse(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $customResponse
        );
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->getTestClientId(),
            $this->getTestUserId()
        );
        
        $result = $agent->process(['test' => 'custom']);
        
        // 验证自定义响应
        $this->assertEquals(Constant::LEAD_QUALITY_HIGH, $result->answer['lead_quality']);
        $this->assertEquals(0.95, $result->answer['confidence']);
        $this->assertEquals('Custom test value', $result->answer['custom_field']);
        $this->assertEquals(888999, $result->recordId);
        $this->assertArrayHasKey('timestamp', $result->answer);
    }

    /**
     * 测试参数验证功能
     */
    public function testParameterValidation()
    {
        // 清空之前的调用日志
        $this->clearAgentCallLog();

        $this->setQualityAnalysisScenario('high_quality');
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->getTestClientId(),
            $this->getTestUserId()
        );
        
        $testParams = [
            'client_profile' => json_encode(['company' => 'Test Corp']),
            'buyer_profile' => json_encode(['industry' => 'Technology']),
            'additional_data' => 'test_value',
            'timestamp' => time()
        ];
        
        $agent->process($testParams);
        
        // 验证参数被正确传递
        $this->assertAgentCalledWith(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $testParams
        );
        
        // 验证最后调用的参数
        $lastCall = AiAgentFactoryMockery::getLastAgentCallParams(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS
        );
        
        $this->assertEquals($testParams, $lastCall['params']);
        $this->assertEquals($this->getTestClientId(), $lastCall['clientId']);
        $this->assertEquals($this->getTestUserId(), $lastCall['userId']);
    }

    /**
     * 测试场景配置功能
     */
    public function testScenarioConfiguration()
    {
        // 清空之前的调用日志
        $this->clearAgentCallLog();

        // 设置复杂场景
        $this->setAiAgentTestScenario('integration_test_scenario', [
            'lead_quality' => Constant::LEAD_QUALITY_HIGH,
            'confidence_threshold' => 0.9,
            'industry_focus' => 'Technology',
            'test_mode' => true
        ]);
        
        $agent = AiAgentFactory::createAgent(
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            $this->getTestClientId(),
            $this->getTestUserId()
        );
        
        $result = $agent->process(['test' => 'scenario']);
        
        // 验证场景配置生效
        $this->assertEquals(Constant::LEAD_QUALITY_HIGH, $result->answer['lead_quality']);
        
        // 验证当前场景
        $currentScenario = AiAgentFactoryMockery::getCurrentScenario();
        $this->assertEquals('integration_test_scenario', $currentScenario['name']);
        $this->assertTrue($currentScenario['config']['test_mode']);
        $this->assertEquals('Technology', $currentScenario['config']['industry_focus']);
    }

    /**
     * 测试性能和内存使用
     */
    public function testPerformanceAndMemory()
    {
        // 清空之前的调用日志
        $this->clearAgentCallLog();

        $startTime = microtime(true);
        $startMemory = memory_get_usage();
        
        // 执行大量Agent调用
        for ($i = 0; $i < 100; $i++) {
            $agent = AiAgentFactory::createAgent(
                \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
                $this->getTestClientId(),
                $this->getTestUserId()
            );
            
            $agent->process(['iteration' => $i]);
        }
        
        $endTime = microtime(true);
        $endMemory = memory_get_usage();
        
        $executionTime = $endTime - $startTime;
        $memoryUsed = $endMemory - $startMemory;
        
        // 验证性能指标
        $this->assertLessThan(1.0, $executionTime, 'Mock execution should be fast (< 1 second for 100 calls)');
        $this->assertLessThan(10 * 1024 * 1024, $memoryUsed, 'Memory usage should be reasonable (< 10MB)');
        
        // 验证调用统计
        $this->assertAgentCallCount(\AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS, 100);
        
        echo "\n=== Performance Test Results ===\n";
        echo "Execution Time: " . round($executionTime * 1000, 2) . " ms\n";
        echo "Memory Used: " . round($memoryUsed / 1024, 2) . " KB\n";
        echo "Average per call: " . round(($executionTime * 1000) / 100, 2) . " ms\n";
        echo "===============================\n";
    }

    /**
     * 判断Agent是否返回数组（继承自ProductUsageAgent）
     */
    private function isArrayReturnAgent(int $sceneType): bool
    {
        $arrayReturnAgents = [
            \AiAgent::AI_AGENT_SCENE_TYPE_SDR_LEAD_QUALITY_ANALYSIS,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_EDM_MAIL_WRITER,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_SELLER_INDUSTRY_ANALYZE,
            \AiAgent::AI_AGENT_SCENE_TYPE_AI_SDR_HUTCH_LEAD_QUALITY_ANALYSIS
        ];

        return in_array($sceneType, $arrayReturnAgents);
    }
}
